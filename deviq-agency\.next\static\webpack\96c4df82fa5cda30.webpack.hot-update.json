{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/AdvancedAnimations.tsx", "(app-pages-browser)/./components/ModernSinglePage.tsx", "(app-pages-browser)/./components/MovingGraphics.tsx", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-element.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/handle-window.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/resize/index.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs", "(app-pages-browser)/./node_modules/motion-dom/dist/es/value/spring-value.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"]}